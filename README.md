# BRAIN Alpha Automation System

A systematic and modular Python automation system for creating, submitting, and analyzing alpha strategies using the WorldQuant BRAIN API.

## 🏗️ **System Architecture**

### **Modular Design**
- **Core Engine**: Session management and API interaction
- **Strategy Generator**: Alpha expression creation and parameter sweeping
- **Performance Analyzer**: Results analysis and reporting
- **Configuration Management**: Centralized settings and parameters

### **Key Features**
- ✅ **Smart Session Management**: 23-hour session persistence with login limit protection
- ✅ **Advanced Strategy Generation**: Momentum, mean reversion, correlation, and custom strategies
- ✅ **Comprehensive Analysis**: Sharpe ratio, PnL, turnover, and fitness tracking
- ✅ **Batch Processing**: Intelligent batching with API rate limiting
- ✅ **Systematic Testing**: Comprehensive test suite with reliable expressions
- ✅ **Clean Configuration**: JSON-based configuration management

## 🚀 **Quick Start**

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Setup Credentials
Create `.brain_credentials` file:
```json
["<EMAIL>", "your_password"]
```

### 3. Run the System
```bash
# Interactive mode (recommended)
python main.py

# Specific strategies
python main.py --strategy momentum_sweep --batch-size 10
python main.py --strategy comprehensive --batch-size 15

# System status check
python main.py --status

# Authentication only
python main.py --authenticate
```

## 📚 **Usage Examples**

### Basic Pipeline
```python
from brain_alpha import BrainAlphaAutomator

automator = BrainAlphaAutomator()
results = automator.automated_alpha_pipeline()
```

### Advanced Batch Processing
```python
# Momentum strategy sweep
results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=10)

# Custom expressions
custom_exprs = [
    "rank(close / ts_mean(close, 20))",
    "correlation(close, volume, 15)",
    "-(close - ts_mean(close, 15)) / ts_std(close, 15)"
]
results = automator.automated_alpha_pipeline(custom_expressions=custom_exprs)
```

### Using Individual Components
```python
from brain_alpha import AlphaGenerator, PerformanceAnalyzer

# Generate strategies
generator = AlphaGenerator()
momentum_exprs = generator.generate_momentum_variations(count=5)

# Analyze results
analyzer = PerformanceAnalyzer()
performance_summary = analyzer.analyze_results(results)
analyzer.save_to_csv(results)
```

## 📊 **Available Strategies**

| Strategy | Description | Sample Expressions |
|----------|-------------|-------------------|
| **momentum_sweep** | Momentum with varying lookback periods | `close / delay(close, N) - 1`<br>`rank(close / delay(close, N))` |
| **mean_reversion_sweep** | Mean reversion with different windows | `-(close - ts_mean(close, N)) / ts_std(close, N)`<br>`rank(ts_mean(close, N) - close)` |
| **correlation_sweep** | Price-volume correlation strategies | `correlation(close, volume, N)`<br>`rank(correlation(close, volume, N))` |
| **comprehensive** | Mixed strategy approach | Combines all strategy types |

## ⚙️ **Configuration**

Edit `config.json` to customize:
```json
{
  "simulation": {
    "region": "USA",
    "universe": "TOP3000",
    "testPeriod": "P1Y6M"
  },
  "performance": {
    "min_sharpe_ratio": 0.3,
    "top_n_results": 10
  },
  "batch": {
    "default_batch_size": 8,
    "inter_batch_delay": 30
  }
}
```

## 📁 **Project Structure**

```
brain_alpha/
├── core/
│   ├── automator.py      # Main automation engine
│   └── session_manager.py # Authentication & session management
├── strategies/
│   └── generator.py      # Alpha expression generation
├── analysis/
│   └── performance.py    # Performance analysis & reporting
└── config/
    └── settings.py       # Configuration management

tests/
└── test_system.py        # Comprehensive test suite

main.py                   # Main entry point
config.json              # System configuration
```

## 🧪 **Testing**

Run the comprehensive test suite:
```bash
# Run all tests
python tests/test_system.py

# Run specific tests
python tests/test_system.py --test auth
python tests/test_system.py --test expressions
python tests/test_system.py --test reliable
```

## 🔧 **Session Management**

The system includes intelligent session management:
- **23-hour session persistence** to avoid re-authentication
- **Daily login limit protection** (20/25 safety buffer)
- **Automatic session recovery** on system restart
- **Biometric authentication support** for enhanced security

Check session status:
```bash
python main.py --status
```

## 📈 **Performance Analysis**

Automatic analysis includes:
- ✅ **Sharpe Ratio Calculation**
- ✅ **PnL and Turnover Analysis**
- ✅ **Fitness Score Tracking**
- ✅ **Top Performer Filtering**
- ✅ **CSV Export for Further Analysis**
- ✅ **Performance Summary Reports**

## 🚨 **Important Notes**

1. **Start Small**: Begin with 3-5 alphas to test the system
2. **Monitor Limits**: System automatically respects API rate limits
3. **Session Persistence**: Reuses sessions to minimize login count
4. **Reliable Expressions**: Use tested expressions for guaranteed success
5. **Results Export**: All results automatically saved to timestamped CSV files

## 🎯 **Quick Commands**

```bash
# Interactive mode (recommended for first time)
python main.py

# Quick momentum test
python main.py --strategy momentum_sweep --batch-size 5

# System health check
python main.py --status

# Test reliable expressions
python main.py --reliable

# Run comprehensive test suite
python tests/test_system.py
```

---

## 🎉 **Ready to Launch!**

Your systematic alpha automation system is ready for production use!

**Start with:** `python main.py` for interactive mode or `python main.py --strategy comprehensive --batch-size 10` for immediate automation.
