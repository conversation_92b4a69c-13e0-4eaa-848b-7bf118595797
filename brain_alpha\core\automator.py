"""
Main automation engine for BRAIN Alpha system
Coordinates session management, strategy generation, and performance analysis
"""

import time
import json
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin

from .session_manager import SessionManager
from ..strategies.generator import AlphaGenerator
from ..analysis.performance import PerformanceAnalyzer
from ..config.settings import config


class BrainAlphaAutomator:
    """Main automation engine for BRAIN Alpha strategies"""
    
    def __init__(self, credentials_file: str = '.brain_credentials'):
        self.session_manager = SessionManager(credentials_file)
        self.generator = AlphaGenerator()
        self.analyzer = PerformanceAnalyzer()
        self.base_url = 'https://api.worldquantbrain.com'
    
    @property
    def authenticated(self) -> bool:
        """Check if currently authenticated"""
        return self.session_manager.authenticated
    
    def authenticate(self, force_new_login: bool = False) -> bool:
        """Authenticate with BRAIN API"""
        return self.session_manager.authenticate(force_new_login)
    
    def ensure_authenticated(self) -> bool:
        """Ensure authentication before API calls"""
        return self.session_manager.ensure_authenticated()
    
    def submit_alpha(self, alpha_expression: str, settings: Optional[Dict[str, Any]] = None) -> str:
        """Submit an alpha for simulation"""
        if not self.authenticated:
            raise Exception("Not authenticated. Call authenticate() first.")
        
        if settings is None:
            settings = config.get_simulation_settings()
        
        simulation_data = {
            'type': 'REGULAR',
            'settings': settings,
            'regular': alpha_expression
        }
        
        print(f"📤 Submitting alpha: {alpha_expression}")
        session = self.session_manager.get_session()
        response = session.post(f'{self.base_url}/simulations', json=simulation_data)
        
        if response.status_code == 201:
            simulation_url = response.headers['Location']
            print(f"✅ Alpha submitted successfully. Tracking at: {simulation_url}")
            
            # Ensure we have the correct URL format
            if not simulation_url.startswith('http'):
                simulation_url = f"{self.base_url}{simulation_url}"
            
            return simulation_url
        else:
            raise Exception(f"❌ Alpha submission failed: {response.status_code} - {response.text}")
    
    def wait_for_simulation(self, simulation_url: str) -> str:
        """Wait for simulation to complete and return alpha ID"""
        print("⏳ Waiting for simulation to complete...")
        
        # Fix URL construction
        if simulation_url.startswith('http'):
            full_url = simulation_url
        else:
            full_url = f'{self.base_url}{simulation_url}'
        
        print(f"🔍 Monitoring simulation at: {full_url}")
        session = self.session_manager.get_session()
        
        while True:
            try:
                response = session.get(full_url)
                
                if response.status_code != 200:
                    print(f"⚠️ Warning: Received status code {response.status_code}")
                    time.sleep(10)
                    continue
                
                data = response.json()
                
                if response.headers.get("Retry-After"):
                    retry_after = float(response.headers["Retry-After"])
                    retry_after_int = int(retry_after) if retry_after == int(retry_after) else int(retry_after) + 1
                    print(f"⏱️ Simulation in progress. Sleeping for {retry_after_int} seconds...")
                    time.sleep(retry_after_int)
                else:
                    if data.get('status') == 'COMPLETE':
                        alpha_id = data.get('alpha')
                        print(f"🎉 Simulation completed! Alpha ID: {alpha_id}")
                        return alpha_id
                    elif data.get('status') in ['ERROR', 'FAIL', 'TIMEOUT']:
                        raise Exception(f"❌ Simulation failed with status: {data.get('status')} - {data.get('message', 'No error message')}")
                    else:
                        print(f"📊 Simulation status: {data.get('status')}")
                        time.sleep(10)  # Wait 10 seconds before checking again
            
            except Exception as e:
                print(f"⚠️ Error checking simulation status: {e}")
                print("🔄 Retrying in 15 seconds...")
                time.sleep(15)
    
    def get_alpha_results(self, alpha_id: str) -> Dict[str, Any]:
        """Retrieve alpha simulation results"""
        print(f"📊 Retrieving results for alpha: {alpha_id}")
        session = self.session_manager.get_session()
        
        # Get alpha details
        alpha_response = session.get(f'{self.base_url}/alphas/{alpha_id}')
        
        if alpha_response.status_code != 200:
            print(f"⚠️ Warning: Could not get alpha details (status: {alpha_response.status_code})")
            alpha_data = {}
        else:
            try:
                alpha_data = alpha_response.json()
            except json.JSONDecodeError:
                print("⚠️ Warning: Could not parse alpha details JSON")
                alpha_data = {}
        
        # Get available recordsets
        recordsets_response = session.get(f'{self.base_url}/alphas/{alpha_id}/recordsets')
        
        if recordsets_response.status_code != 200:
            print(f"⚠️ Warning: Could not get recordsets (status: {recordsets_response.status_code})")
            recordsets = []
        else:
            try:
                recordsets = recordsets_response.json()
            except json.JSONDecodeError:
                print("⚠️ Warning: Could not parse recordsets JSON")
                recordsets = []
        
        results = {
            'alpha_details': alpha_data,
            'available_recordsets': recordsets,
            'performance_data': {}
        }
        
        # Get key performance metrics
        key_metrics = ['pnl', 'sharpe', 'turnover', 'yearly-stats']
        
        for metric in key_metrics:
            try:
                # Wait for recordset to be ready
                while True:
                    metric_response = session.get(f'{self.base_url}/alphas/{alpha_id}/recordsets/{metric}')
                    if metric_response.headers.get("Retry-After"):
                        retry_after = float(metric_response.headers["Retry-After"])
                        retry_after_int = int(retry_after) if retry_after == int(retry_after) else int(retry_after) + 1
                        print(f"⏱️ Waiting for {metric} data. Sleeping for {retry_after_int} seconds...")
                        time.sleep(retry_after_int)
                    else:
                        if metric_response.status_code == 200:
                            try:
                                results['performance_data'][metric] = metric_response.json()
                                print(f"✅ Retrieved {metric} data")
                            except json.JSONDecodeError:
                                print(f"⚠️ Warning: Could not parse {metric} data")
                                results['performance_data'][metric] = {}
                        else:
                            print(f"⚠️ Warning: Could not get {metric} data (status: {metric_response.status_code})")
                            results['performance_data'][metric] = {}
                        break
            except Exception as e:
                print(f"⚠️ Could not retrieve {metric}: {e}")
        
        return results
    
    def submit_multiple_alphas(self, expressions_list: List[str], 
                             settings: Optional[Dict[str, Any]] = None) -> str:
        """Submit multiple alphas simultaneously"""
        if len(expressions_list) < 2 or len(expressions_list) > 10:
            raise ValueError("Multiple simulations require 2-10 expressions")
        
        if not self.authenticated:
            raise Exception("Not authenticated. Call authenticate() first.")
        
        if settings is None:
            settings = config.get_simulation_settings()
        
        simulations = []
        for expr in expressions_list:
            simulations.append({
                'type': 'REGULAR',
                'settings': settings,
                'regular': expr
            })
        
        session = self.session_manager.get_session()
        response = session.post(f'{self.base_url}/simulations', json=simulations)
        
        if response.status_code == 201:
            simulation_url = response.headers['Location']
            print(f"✅ Multiple alphas submitted successfully. Tracking at: {simulation_url}")
            return simulation_url
        else:
            raise Exception(f"❌ Multiple alpha submission failed: {response.status_code} - {response.text}")
    
    def process_single_alpha(self, expression: str) -> Dict[str, Any]:
        """Process a single alpha from submission to results"""
        # Submit alpha
        simulation_url = self.submit_alpha(expression)

        # Wait for completion
        alpha_id = self.wait_for_simulation(simulation_url)

        # Get results
        alpha_results = self.get_alpha_results(alpha_id)
        alpha_results['expression'] = expression

        return alpha_results

    def automated_alpha_pipeline(self, expression_types: Optional[List[str]] = None,
                                custom_expressions: Optional[List[str]] = None) -> Optional[List[Dict[str, Any]]]:
        """Complete automated pipeline: authenticate, create, submit, and retrieve results"""
        try:
            # Step 1: Ensure authentication (reuse session if possible)
            self.ensure_authenticated()

            # Step 2: Create alpha expressions
            expressions = []
            if custom_expressions:
                expressions = custom_expressions
            else:
                if expression_types is None:
                    expression_types = ["simple", "momentum", "mean_reversion"]

                for expr_type in expression_types:
                    expressions.append(self.generator.create_expression(expr_type))

            results = []

            # Step 3: Submit and process each alpha
            for i, expression in enumerate(expressions):
                print(f"\n--- Processing Alpha {i+1}/{len(expressions)} ---")

                try:
                    alpha_result = self.process_single_alpha(expression)
                    results.append(alpha_result)
                    print(f"✅ Alpha {i+1} completed successfully!")
                except Exception as e:
                    print(f"❌ Alpha {i+1} failed: {e}")
                    continue

            return results

        except Exception as e:
            print(f"❌ Pipeline failed: {e}")
            return None

    def batch_alpha_generation_pipeline(self, strategy: str = "comprehensive",
                                      batch_size: int = 5) -> tuple:
        """Advanced pipeline for generating and testing multiple alpha strategies"""
        try:
            # Step 1: Ensure authentication
            self.ensure_authenticated()

            # Step 2: Generate expressions based on strategy
            all_expressions = self.generator.generate_strategy_batch(strategy, batch_size)
            print(f"📊 Generated {len(all_expressions)} expressions for testing")

            # Step 3: Process in batches to avoid overwhelming the API
            all_results = []
            actual_batch_size = min(config.batch.default_batch_size, len(all_expressions))

            for i in range(0, len(all_expressions), actual_batch_size):
                batch = all_expressions[i:i + actual_batch_size]
                print(f"\n=== Processing Batch {i//actual_batch_size + 1} ===")

                batch_results = self.automated_alpha_pipeline(custom_expressions=batch)
                if batch_results:
                    all_results.extend(batch_results)

                # Small delay between batches
                if i + actual_batch_size < len(all_expressions):
                    print(f"⏱️ Waiting {config.batch.inter_batch_delay} seconds before next batch...")
                    time.sleep(config.batch.inter_batch_delay)

            # Step 4: Analyze results
            if all_results:
                print(f"\n=== Analysis of {len(all_results)} Alphas ===")
                performance_summary = self.analyzer.analyze_results(all_results)

                # Save results
                csv_file = self.analyzer.save_to_csv(all_results)

                # Show top performers
                top_performers = self.analyzer.filter_top_performers(
                    all_results,
                    min_sharpe=config.performance.min_sharpe_ratio,
                    top_n=config.performance.top_n_results
                )

                # Print summary
                self.analyzer.print_performance_summary(all_results, top_n=5)

                return all_results, performance_summary, top_performers

            return None, None, None

        except Exception as e:
            print(f"❌ Batch pipeline failed: {e}")
            return None, None, None
