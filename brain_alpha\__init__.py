"""
BRAIN Alpha Automation System

A systematic and modular approach to automated alpha strategy generation,
testing, and analysis using the WorldQuant BRAIN API.
"""

from .core.automator import BrainAlphaAutomator
from .core.session_manager import SessionManager
from .strategies.generator import AlphaGenerator
from .analysis.performance import PerformanceAnalyzer
from .config.settings import Config

__version__ = "2.0.0"
__author__ = "BRAIN Alpha Automation Team"

__all__ = [
    "BrainAlphaAutomator",
    "SessionManager", 
    "AlphaGenerator",
    "PerformanceAnalyzer",
    "Config"
]
