"""
Session management for BRAIN API authentication
Handles login limits, session persistence, and authentication state
"""

import json
import os
import pickle
import requests
from datetime import datetime, timedelta
from typing import Optional, Tuple
from urllib.parse import urljoin


class SessionManager:
    """Manages BRAIN API authentication sessions with persistence and login limits"""
    
    def __init__(self, credentials_file: str = '.brain_credentials'):
        self.session = requests.Session()
        self.base_url = 'https://api.worldquantbrain.com'
        self.session_file = '.brain_session.pkl'
        self.login_count_file = '.brain_login_count.json'
        self.authenticated = False
        self.credentials_file = credentials_file
        self._load_credentials()
        self._load_session()
    
    def _load_credentials(self) -> None:
        """Load credentials from JSON file"""
        try:
            # Try current directory first, then home directory
            if not self.credentials_file.startswith('.') and not self.credentials_file.startswith('/'):
                self.credentials_file = f"./{self.credentials_file}"

            try:
                with open(self.credentials_file, 'r') as f:
                    self.session.auth = tuple(json.load(f))
                print(f"✅ Credentials loaded from {self.credentials_file}")
            except FileNotFoundError:
                # Fallback to home directory
                from os.path import expanduser
                home_path = expanduser(f"~/{self.credentials_file}")
                with open(home_path, 'r') as f:
                    self.session.auth = tuple(json.load(f))
                print(f"✅ Credentials loaded from {home_path}")

        except FileNotFoundError:
            print(f"❌ Credentials file {self.credentials_file} not found. Please create it with format: ['email', 'password']")
            raise

    def _load_session(self) -> bool:
        """Load existing session to avoid re-authentication"""
        try:
            if os.path.exists(self.session_file):
                with open(self.session_file, 'rb') as f:
                    session_data = pickle.load(f)

                # Check if session is still valid (less than 23 hours old)
                if datetime.now() - session_data['timestamp'] < timedelta(hours=23):
                    self.session.cookies.update(session_data['cookies'])
                    self.authenticated = True
                    print("✅ Loaded existing session (avoiding new login)")
                    return True
                else:
                    print("⚠️ Existing session expired")
                    os.remove(self.session_file)
        except Exception as e:
            print(f"Could not load session: {e}")

        return False

    def _save_session(self) -> None:
        """Save current session for reuse"""
        try:
            session_data = {
                'cookies': dict(self.session.cookies),
                'timestamp': datetime.now()
            }
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
            print("💾 Session saved for reuse")
        except Exception as e:
            print(f"Could not save session: {e}")

    def _check_login_limit(self) -> None:
        """Check and update daily login count"""
        today = datetime.now().strftime('%Y-%m-%d')

        try:
            if os.path.exists(self.login_count_file):
                with open(self.login_count_file, 'r') as f:
                    login_data = json.load(f)
            else:
                login_data = {}

            # Reset count if it's a new day
            if login_data.get('date') != today:
                login_data = {'date': today, 'count': 0}

            current_count = login_data.get('count', 0)

            if current_count >= 20:  # Conservative limit (5 below max)
                raise Exception(f"⚠️ DAILY LOGIN LIMIT REACHED! ({current_count}/25 logins used today). Please wait until tomorrow or use existing session.")

            # Increment count
            login_data['count'] = current_count + 1

            with open(self.login_count_file, 'w') as f:
                json.dump(login_data, f)

            print(f"📊 Login count: {login_data['count']}/25 for today")

            if login_data['count'] >= 15:
                print("⚠️ WARNING: Approaching daily login limit! Consider using session persistence.")

        except Exception as e:
            if "DAILY LOGIN LIMIT" in str(e):
                raise e
            print(f"Could not check login limit: {e}")

    def authenticate(self, force_new_login: bool = False) -> bool:
        """Authenticate with the BRAIN API with session persistence"""
        
        # Check if we already have a valid session
        if self.authenticated and not force_new_login:
            print("✅ Already authenticated with existing session")
            return True

        # Check daily login limit before attempting new login
        if not force_new_login:
            self._check_login_limit()

        print("🔐 Attempting authentication...")
        response = self.session.post(f'{self.base_url}/authentication')

        if response.status_code == 401:
            if response.headers.get("WWW-Authenticate") == "persona":
                # Handle biometric authentication
                auth_url = urljoin(response.url, response.headers["Location"])
                print(f"🔒 Biometric authentication required")
                print(f"🌐 Please complete authentication at: {auth_url}")
                input("Press Enter after completing biometric authentication...")

                # Complete the biometric flow
                biometric_response = self.session.post(urljoin(response.url, response.headers["Location"]))
                print("✅ Biometric authentication completed")

                # Check if biometric auth was successful
                if biometric_response.status_code == 201:
                    print("✅ Authentication successful!")
                    self.authenticated = True
                    self._save_session()  # Save session for reuse
                    return True
                else:
                    raise Exception(f"❌ Biometric authentication failed with status code: {biometric_response.status_code}")
            else:
                raise Exception("❌ Authentication failed: Incorrect email and password")
        elif response.status_code == 201:
            print("✅ Authentication successful!")
            self.authenticated = True
            self._save_session()  # Save session for reuse
            return True
        else:
            raise Exception(f"❌ Authentication failed with status code: {response.status_code}")

    def ensure_authenticated(self) -> bool:
        """Ensure we have a valid authentication before making API calls"""
        if not self.authenticated:
            return self.authenticate()
        return True

    def get_session(self) -> requests.Session:
        """Get the authenticated session"""
        if not self.authenticated:
            raise Exception("Not authenticated. Call authenticate() first.")
        return self.session

    def clear_session(self) -> None:
        """Clear existing session (force new login next time)"""
        if os.path.exists(self.session_file):
            os.remove(self.session_file)
            print("🗑️ Session cleared - next run will require new login")
        self.authenticated = False

    def get_session_status(self) -> dict:
        """Get current session and login status"""
        status = {
            'authenticated': self.authenticated,
            'session_valid': False,
            'session_age_hours': None,
            'daily_logins': 0
        }
        
        # Check session file
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'rb') as f:
                    session_data = pickle.load(f)
                
                age = datetime.now() - session_data['timestamp']
                hours_old = age.total_seconds() / 3600
                status['session_age_hours'] = hours_old
                status['session_valid'] = hours_old < 23
            except:
                pass
        
        # Check login count
        today = datetime.now().strftime('%Y-%m-%d')
        if os.path.exists(self.login_count_file):
            try:
                with open(self.login_count_file, 'r') as f:
                    login_data = json.load(f)
                if login_data.get('date') == today:
                    status['daily_logins'] = login_data.get('count', 0)
            except:
                pass
        
        return status
