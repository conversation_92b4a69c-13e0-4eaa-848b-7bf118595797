"""
Alpha strategy generation module
Handles creation of various alpha expressions and parameter sweeping
"""

import random
import itertools
from typing import List, Dict, Any, Optional


class AlphaGenerator:
    """Generates alpha expressions for different trading strategies"""
    
    def __init__(self):
        self.expression_templates = {
            "simple": "close",
            "momentum": "rank(close / ts_mean(close, 5))",
            "mean_reversion": "-(close - ts_mean(close, 20)) / ts_std(close, 20)",
            "volume_price": "correlation(close, volume, 10)",
            "rsi_based": "rank(close) - 0.5",
            "bollinger": "(close - ts_mean(close, 20)) / ts_std(close, 20)",
            "macd": "ts_mean(close, 12) - ts_mean(close, 26)",
            "volume_momentum": "correlation(close, volume, 20)",
            "price_volume_trend": "rank(close) * rank(volume)",
            "volatility": "ts_std(close, 20)",
            "trend_strength": "correlation(close, ts_rank(close, 20), 10)",
            "simple_momentum": "rank(close)",
            "price_rank": "rank(close)",
            "volume_rank": "rank(volume)",
            "simple_return": "close / ts_mean(close, 2) - 1"
        }
    
    def create_expression(self, expression_type: str = "simple") -> str:
        """Generate alpha expressions using verified working BRAIN operators"""
        return self.expression_templates.get(expression_type, self.expression_templates["simple"])
    
    def generate_parameter_sweep(self, template: str, parameter_ranges: Dict[str, List]) -> List[str]:
        """Generate multiple expressions by sweeping parameters"""
        expressions = []
        
        # Create all combinations of parameters
        param_names = list(parameter_ranges.keys())
        param_values = list(parameter_ranges.values())
        
        for combination in itertools.product(*param_values):
            params = dict(zip(param_names, combination))
            try:
                expression = template.format(**params)
                expressions.append(expression)
            except KeyError as e:
                print(f"Warning: Template parameter {e} not found in parameter_ranges")
        
        return expressions
    
    def generate_momentum_variations(self, lookback_range: tuple = (5, 30), count: int = 10) -> List[str]:
        """Generate momentum alpha variations using verified operators"""
        expressions = []
        lookbacks = random.sample(
            range(lookback_range[0], lookback_range[1] + 1), 
            min(count, lookback_range[1] - lookback_range[0] + 1)
        )
        
        for lookback in lookbacks:
            expressions.extend([
                f"rank(close / ts_mean(close, {lookback}))",
                f"close / ts_mean(close, {lookback}) - 1",
                f"ts_rank(close, {lookback})"
            ])
        
        return expressions[:count]
    
    def generate_mean_reversion_variations(self, window_range: tuple = (10, 50), count: int = 10) -> List[str]:
        """Generate mean reversion alpha variations"""
        expressions = []
        windows = random.sample(
            range(window_range[0], window_range[1] + 1), 
            min(count, window_range[1] - window_range[0] + 1)
        )
        
        for window in windows:
            expressions.extend([
                f"-(close - ts_mean(close, {window})) / ts_std(close, {window})",
                f"rank(ts_mean(close, {window}) - close)",
                f"(ts_mean(close, {window}) - close) / close"
            ])
        
        return expressions[:count]
    
    def generate_correlation_variations(self, window_range: tuple = (10, 30), count: int = 10) -> List[str]:
        """Generate correlation-based alpha variations"""
        expressions = []
        windows = random.sample(
            range(window_range[0], window_range[1] + 1), 
            min(count, window_range[1] - window_range[0] + 1)
        )
        
        for window in windows:
            expressions.extend([
                f"correlation(close, volume, {window})",
                f"correlation(close/delay(close,1)-1, volume, {window})",
                f"rank(correlation(close, volume, {window}))"
            ])
        
        return expressions[:count]
    
    def generate_strategy_batch(self, strategy: str, batch_size: int = 5) -> List[str]:
        """Generate a batch of expressions for a specific strategy"""
        if strategy == "momentum_sweep":
            return self.generate_momentum_variations(count=batch_size)
        elif strategy == "mean_reversion_sweep":
            return self.generate_mean_reversion_variations(count=batch_size)
        elif strategy == "correlation_sweep":
            return self.generate_correlation_variations(count=batch_size)
        elif strategy == "parameter_sweep":
            # Example parameter sweep for momentum strategies
            template = "close / delay(close, {lookback}) - 1"
            param_ranges = {"lookback": list(range(5, 21))}
            return self.generate_parameter_sweep(template, param_ranges)[:batch_size]
        elif strategy == "comprehensive":
            # Mix of different strategies
            momentum_exprs = self.generate_momentum_variations(count=batch_size//3)
            mean_rev_exprs = self.generate_mean_reversion_variations(count=batch_size//3)
            corr_exprs = self.generate_correlation_variations(count=batch_size//3)
            return momentum_exprs + mean_rev_exprs + corr_exprs
        else:
            # Default to basic expressions
            return [self.create_expression(expr_type) for expr_type in 
                   ["simple", "momentum", "mean_reversion", "volume_price", "rsi_based"][:batch_size]]
    
    def get_reliable_expressions(self, count: int = 5) -> List[str]:
        """Get a list of reliable, tested expressions that are guaranteed to work"""
        reliable_expressions = [
            "rank(close)",
            "rank(volume)", 
            "ts_mean(close, 5)",
            "ts_mean(close, 10)",
            "ts_mean(close, 20)",
            "ts_std(close, 5)",
            "ts_std(close, 10)",
            "ts_rank(close, 5)",
            "ts_rank(close, 10)",
            "ts_rank(close, 20)",
            "rank(close / ts_mean(close, 5))",
            "rank(close / ts_mean(close, 10))",
            "rank(close / ts_mean(close, 20))",
            "close / ts_mean(close, 5) - 1",
            "close / ts_mean(close, 10) - 1",
            "(close - ts_mean(close, 20)) / ts_std(close, 20)",
            "ts_mean(close, 5) - ts_mean(close, 20)",
            "rank(volume) * rank(close)",
            "rank(close) - 0.5",
            "ts_rank(close, 10) - 0.5"
        ]
        
        return reliable_expressions[:count]
    
    def get_available_strategies(self) -> List[str]:
        """Get list of available strategy types"""
        return [
            "momentum_sweep",
            "mean_reversion_sweep", 
            "correlation_sweep",
            "parameter_sweep",
            "comprehensive"
        ]
    
    def get_expression_types(self) -> List[str]:
        """Get list of available expression types"""
        return list(self.expression_templates.keys())
