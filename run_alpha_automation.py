#!/usr/bin/env python3
"""
Quick runner script for BRAIN Alpha Automation
Usage: python run_alpha_automation.py [strategy] [batch_size]

Strategies:
- momentum: Test momentum-based alphas
- mean_reversion: Test mean reversion alphas  
- correlation: Test correlation-based alphas
- comprehensive: Mix of all strategies
- custom: Run predefined custom expressions
"""

import sys
import json
from brain_alpha import BrainAlphaAutomator

def load_config():
    """Load configuration from alpha_config.json"""
    try:
        with open('alpha_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Warning: alpha_config.json not found, using defaults")
        return {}

def main():
    config = load_config()
    automator = BrainAlphaAutomator()
    
    # Parse command line arguments
    strategy = sys.argv[1] if len(sys.argv) > 1 else "comprehensive"
    batch_size = int(sys.argv[2]) if len(sys.argv) > 2 else 8
    
    print(f"=== BRAIN Alpha Automation - {strategy.upper()} Strategy ===")
    print(f"Batch size: {batch_size}")
    
    if strategy == "momentum":
        strategy_name = "momentum_sweep"
    elif strategy == "mean_reversion":
        strategy_name = "mean_reversion_sweep"
    elif strategy == "correlation":
        strategy_name = "correlation_sweep"
    elif strategy == "comprehensive":
        strategy_name = "comprehensive"
    elif strategy == "custom":
        # Run predefined custom expressions
        custom_expressions = [
            "rank(close / ts_mean(close, 20))",
            "correlation(close, volume, 15)",
            "ts_rank(close, 10) - 0.5",
            "-(close - ts_mean(close, 15)) / ts_std(close, 15)",
            "rank(volume) * rank(close)",
            "correlation(close, volume, 20)",
            "ts_mean(close, 12) - ts_mean(close, 26)",
            "(close - ts_mean(close, 20)) / ts_std(close, 20)"
        ]
        
        print(f"Running {len(custom_expressions)} custom expressions...")
        results = automator.automated_alpha_pipeline(custom_expressions=custom_expressions)
        
        if results:
            print(f"\nProcessed {len(results)} alphas successfully!")
            
            # Analyze and save results
            summary = automator.analyze_alpha_performance(results)
            csv_file = automator.save_results_to_csv(results)
            top_performers = automator.filter_top_performers(results, min_sharpe=0.3, top_n=5)
            
            print(f"\nTop {len(top_performers)} performers:")
            for i, alpha in enumerate(top_performers, 1):
                print(f"{i}. {alpha['expression']}")
                print(f"   Sharpe: {alpha['sharpe_ratio']:.4f}")
                print(f"   Fitness: {alpha['fitness']}")
        
        return
    else:
        print(f"Unknown strategy: {strategy}")
        print("Available strategies: momentum, mean_reversion, correlation, comprehensive, custom")
        return
    
    # Run batch pipeline
    results, summary, top_performers = automator.batch_alpha_generation_pipeline(strategy_name, batch_size)
    
    if results:
        print(f"\n=== Results Summary ===")
        print(f"Total alphas processed: {len(results)}")
        print(f"Top performers (Sharpe > 0.3): {len(top_performers) if top_performers else 0}")
        
        if top_performers:
            print(f"\nBest performing alphas:")
            for i, alpha in enumerate(top_performers[:3], 1):
                print(f"{i}. {alpha['expression']}")
                print(f"   Sharpe: {alpha['sharpe_ratio']:.4f}")
                print(f"   Alpha ID: {alpha['alpha_id']}")
    else:
        print("No results generated. Check your credentials and API access.")

if __name__ == "__main__":
    main()
