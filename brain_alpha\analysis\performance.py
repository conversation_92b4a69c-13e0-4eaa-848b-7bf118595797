"""
Performance analysis module for alpha strategies
Handles result analysis, ranking, and reporting
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional


class PerformanceAnalyzer:
    """Analyzes and ranks alpha performance"""
    
    def __init__(self):
        pass
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze and rank alpha performance"""
        performance_summary = []
        
        for result in results:
            summary = {
                'expression': result.get('expression', 'N/A'),
                'alpha_id': result.get('alpha_details', {}).get('id', 'N/A'),
                'sharpe_ratio': None,
                'total_pnl': None,
                'turnover': None,
                'fitness': None
            }
            
            # Extract Sharpe ratio
            if 'sharpe' in result.get('performance_data', {}):
                sharpe_data = result['performance_data']['sharpe']
                if 'records' in sharpe_data and sharpe_data['records']:
                    summary['sharpe_ratio'] = sharpe_data['records'][-1].get('value', None)
            
            # Extract total PnL
            if 'pnl' in result.get('performance_data', {}):
                pnl_data = result['performance_data']['pnl']
                if 'records' in pnl_data and pnl_data['records']:
                    summary['total_pnl'] = sum(record.get('value', 0) for record in pnl_data['records'])
            
            # Extract turnover
            if 'turnover' in result.get('performance_data', {}):
                turnover_data = result['performance_data']['turnover']
                if 'records' in turnover_data and turnover_data['records']:
                    summary['turnover'] = sum(record.get('value', 0) for record in turnover_data['records']) / len(turnover_data['records'])
            
            # Extract fitness score
            alpha_details = result.get('alpha_details', {})
            summary['fitness'] = alpha_details.get('fitness', None)
            
            performance_summary.append(summary)
        
        # Sort by Sharpe ratio (descending)
        performance_summary.sort(key=lambda x: x['sharpe_ratio'] or -999, reverse=True)
        
        return performance_summary
    
    def filter_top_performers(self, results: List[Dict[str, Any]], 
                            min_sharpe: float = 0.5, 
                            top_n: int = 10) -> List[Dict[str, Any]]:
        """Filter and return top performing alphas"""
        performance_data = self.analyze_results(results)
        
        # Filter by minimum Sharpe ratio
        filtered = [alpha for alpha in performance_data 
                   if alpha['sharpe_ratio'] and alpha['sharpe_ratio'] >= min_sharpe]
        
        # Return top N
        return filtered[:top_n]
    
    def save_to_csv(self, results: List[Dict[str, Any]], filename: Optional[str] = None) -> str:
        """Save alpha results to CSV file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"alpha_results_{timestamp}.csv"
        
        performance_data = self.analyze_results(results)
        df = pd.DataFrame(performance_data)
        df.to_csv(filename, index=False)
        print(f"📊 Results saved to {filename}")
        return filename
    
    def generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a comprehensive summary report"""
        performance_data = self.analyze_results(results)
        
        # Calculate summary statistics
        sharpe_ratios = [alpha['sharpe_ratio'] for alpha in performance_data if alpha['sharpe_ratio'] is not None]
        fitness_scores = [alpha['fitness'] for alpha in performance_data if alpha['fitness'] is not None]
        
        summary = {
            'total_alphas': len(results),
            'successful_alphas': len([alpha for alpha in performance_data if alpha['sharpe_ratio'] is not None]),
            'avg_sharpe_ratio': sum(sharpe_ratios) / len(sharpe_ratios) if sharpe_ratios else 0,
            'max_sharpe_ratio': max(sharpe_ratios) if sharpe_ratios else 0,
            'min_sharpe_ratio': min(sharpe_ratios) if sharpe_ratios else 0,
            'avg_fitness': sum(fitness_scores) / len(fitness_scores) if fitness_scores else 0,
            'top_performers': self.filter_top_performers(results, min_sharpe=0.3, top_n=5),
            'timestamp': datetime.now().isoformat()
        }
        
        return summary
    
    def print_performance_summary(self, results: List[Dict[str, Any]], top_n: int = 5) -> None:
        """Print a formatted performance summary to console"""
        performance_data = self.analyze_results(results)
        summary = self.generate_summary_report(results)
        
        print("\n" + "="*60)
        print("📊 ALPHA PERFORMANCE SUMMARY")
        print("="*60)
        print(f"Total Alphas Processed: {summary['total_alphas']}")
        print(f"Successful Alphas: {summary['successful_alphas']}")
        print(f"Average Sharpe Ratio: {summary['avg_sharpe_ratio']:.4f}")
        print(f"Best Sharpe Ratio: {summary['max_sharpe_ratio']:.4f}")
        
        print(f"\n🏆 TOP {top_n} PERFORMERS:")
        print("-" * 60)
        
        for i, alpha in enumerate(performance_data[:top_n], 1):
            print(f"\n{i}. Expression: {alpha['expression']}")
            print(f"   Alpha ID: {alpha['alpha_id']}")
            print(f"   Sharpe Ratio: {alpha['sharpe_ratio']:.4f}" if alpha['sharpe_ratio'] else "   Sharpe Ratio: N/A")
            print(f"   Fitness: {alpha['fitness']:.4f}" if alpha['fitness'] else "   Fitness: N/A")
            print(f"   Total PnL: {alpha['total_pnl']:.4f}" if alpha['total_pnl'] else "   Total PnL: N/A")
        
        print("\n" + "="*60)
    
    def get_performance_metrics(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Extract performance metrics from a single result"""
        metrics = {
            'sharpe_ratio': None,
            'total_pnl': None,
            'avg_turnover': None,
            'fitness': None,
            'max_drawdown': None,
            'volatility': None
        }
        
        performance_data = result.get('performance_data', {})
        
        # Sharpe ratio
        if 'sharpe' in performance_data:
            sharpe_data = performance_data['sharpe']
            if 'records' in sharpe_data and sharpe_data['records']:
                metrics['sharpe_ratio'] = sharpe_data['records'][-1].get('value')
        
        # PnL
        if 'pnl' in performance_data:
            pnl_data = performance_data['pnl']
            if 'records' in pnl_data and pnl_data['records']:
                pnl_values = [record.get('value', 0) for record in pnl_data['records']]
                metrics['total_pnl'] = sum(pnl_values)
        
        # Turnover
        if 'turnover' in performance_data:
            turnover_data = performance_data['turnover']
            if 'records' in turnover_data and turnover_data['records']:
                turnover_values = [record.get('value', 0) for record in turnover_data['records']]
                metrics['avg_turnover'] = sum(turnover_values) / len(turnover_values)
        
        # Fitness from alpha details
        alpha_details = result.get('alpha_details', {})
        metrics['fitness'] = alpha_details.get('fitness')
        
        return metrics
    
    def compare_strategies(self, results_by_strategy: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Compare performance across different strategies"""
        comparison = {}
        
        for strategy_name, strategy_results in results_by_strategy.items():
            performance_data = self.analyze_results(strategy_results)
            sharpe_ratios = [alpha['sharpe_ratio'] for alpha in performance_data if alpha['sharpe_ratio'] is not None]
            
            comparison[strategy_name] = {
                'count': len(strategy_results),
                'successful_count': len(sharpe_ratios),
                'avg_sharpe': sum(sharpe_ratios) / len(sharpe_ratios) if sharpe_ratios else 0,
                'max_sharpe': max(sharpe_ratios) if sharpe_ratios else 0,
                'success_rate': len(sharpe_ratios) / len(strategy_results) if strategy_results else 0,
                'top_alpha': performance_data[0] if performance_data else None
            }
        
        return comparison
