# 🚀 BRAIN Alpha Automation - Quick Start Guide

Your systematic alpha automation system is **READY TO GO!**

## ✅ System Status
- **Modular Architecture**: ✅ Clean, systematic design
- **Dependencies**: ✅ Minimal requirements (requests, pandas)
- **API Integration**: ✅ Ready for WorldQuant BRAIN
- **Session Management**: ✅ Smart login limit protection (25/day max)
- **Testing Framework**: ✅ Comprehensive test suite

## 🚨 **IMPORTANT: Login Limit Protection**
- **BRAIN API Limit**: Maximum 25 logins per day
- **Our Protection**: System stops at 20 logins (safety buffer)
- **Session Persistence**: Automatically reuses sessions for 23 hours
- **Smart Management**: Avoids unnecessary logins

---

## 🔧 **First Steps**
```bash
# 1. Check system status
python main.py --status

# 2. Run comprehensive tests
python tests/test_system.py

# 3. Authenticate and save session
python main.py --authenticate
```

## 🎯 Start Automating NOW!

### Option 1: Interactive Mode (Recommended)
```bash
python main.py
```
Choose from menu options:
- `1` - Basic pipeline (3 default expressions)
- `2` - Momentum strategy sweep
- `3` - Mean reversion sweep
- `4` - Correlation strategies
- `5` - Comprehensive multi-strategy
- `7` - Reliable expressions test
- `8` - System status check

### Option 2: Command Line (Quick automation)
```bash
# Test momentum strategies
python main.py --strategy momentum_sweep --batch-size 10

# Test comprehensive strategies
python main.py --strategy comprehensive --batch-size 15

# Test reliable expressions
python main.py --reliable

# Check system status
python main.py --status
```

### Option 3: Python Code (Advanced)
```python
from brain_alpha import BrainAlphaAutomator

# Initialize
automator = BrainAlphaAutomator()

# Quick start - 3 default alphas
results = automator.automated_alpha_pipeline()

# Advanced - batch processing
results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=10)
```

---

## 🔥 What Happens When You Run It

1. **Authentication**: Automatic login to BRAIN API
2. **Expression Generation**: Creates alpha expressions based on strategy
3. **Submission**: Submits alphas for simulation
4. **Monitoring**: Waits for completion with progress updates
5. **Results**: Retrieves performance data (Sharpe, PnL, turnover)
6. **Analysis**: Ranks alphas and saves to CSV
7. **Summary**: Shows top performers

---

## 📊 Example Output

```
=== Processing Alpha 1/10 ===
Submitting alpha: close / delay(close, 15) - 1
Alpha submitted successfully. Tracking at: /simulations/12345
Waiting for simulation to complete...
Simulation completed! Alpha ID: alpha_67890
Retrieved pnl data
Retrieved sharpe data
Alpha 1 completed successfully!

Top 3 Performers:
1. close / delay(close, 15) - 1
   Sharpe: 0.8234
   Alpha ID: alpha_67890

Results saved to alpha_results_20240728_143022.csv
```

---

## 🎛️ Available Strategies

| Strategy | Description | Sample Expression |
|----------|-------------|-------------------|
| **momentum** | Price momentum with various lookbacks | `close / delay(close, 10) - 1` |
| **mean_reversion** | Mean reversion with different windows | `-(close - ts_mean(close, 20)) / ts_std(close, 20)` |
| **correlation** | Price-volume relationships | `correlation(close, volume, 15)` |
| **comprehensive** | Mix of all strategies | Multiple types combined |
| **custom** | Your own expressions | User-defined |

---

## 🔧 Customization

### Add Your Own Expressions
Edit `run_alpha_automation.py` and modify the `custom_expressions` list:
```python
custom_expressions = [
    "your_expression_1",
    "your_expression_2", 
    "your_expression_3"
]
```

### Parameter Sweeping
```python
template = "correlation(close, volume, {window})"
param_ranges = {"window": [10, 15, 20, 25, 30]}
expressions = automator.generate_parameter_sweep_expressions(template, param_ranges)
```

---

## 📈 Performance Analysis

The system automatically:
- ✅ Calculates Sharpe ratios
- ✅ Tracks total PnL
- ✅ Measures turnover
- ✅ Records fitness scores
- ✅ Exports to CSV
- ✅ Filters top performers

---

## 🚨 Important Notes

1. **Start Small**: Begin with 3-5 alphas to test the system
2. **Monitor Limits**: BRAIN API has rate limits - the system handles this automatically
3. **Biometric Auth**: If prompted, complete biometric authentication in browser
4. **Results**: All results are saved to timestamped CSV files
5. **Top Performers**: Look for Sharpe ratios > 0.3 as good performers

---

## 🎉 Ready to Launch!

**Your system is 100% ready for alpha automation!**

**Quick Start Command:**
```bash
python main.py --strategy comprehensive --batch-size 10
```

This will:
- Authenticate with your credentials (reusing session if available)
- Generate 10 diverse alpha strategies
- Submit them for simulation
- Monitor progress automatically
- Analyze and rank results
- Save everything to CSV

**Let the systematic automation begin!** 🚀
