"""
Configuration management for BRAIN Alpha Automation System
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class SimulationSettings:
    """Default simulation settings for BRAIN API"""
    instrumentType: str = 'EQUITY'
    region: str = 'USA'
    universe: str = 'TOP3000'
    delay: int = 1
    decay: int = 15
    neutralization: str = 'SUBINDUSTRY'
    truncation: float = 0.08
    pasteurization: str = 'ON'
    testPeriod: str = 'P1Y6M'
    unitHandling: str = 'VERIFY'
    nanHandling: str = 'OFF'
    language: str = 'FASTEXPR'
    visualization: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API submission"""
        return {
            'instrumentType': self.instrumentType,
            'region': self.region,
            'universe': self.universe,
            'delay': self.delay,
            'decay': self.decay,
            'neutralization': self.neutralization,
            'truncation': self.truncation,
            'pasteurization': self.pasteurization,
            'testPeriod': self.testPeriod,
            'unitHandling': self.unitHandling,
            'nanHandling': self.nanHandling,
            'language': self.language,
            'visualization': self.visualization,
        }


@dataclass
class PerformanceFilters:
    """Performance filtering criteria"""
    min_sharpe_ratio: float = 0.3
    min_fitness: float = 0.5
    top_n_results: int = 10


@dataclass
class BatchSettings:
    """Batch processing settings"""
    default_batch_size: int = 8
    max_batch_size: int = 15
    inter_batch_delay: int = 30  # seconds


class Config:
    """Central configuration management"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.simulation = SimulationSettings()
        self.performance = PerformanceFilters()
        self.batch = BatchSettings()
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file if it exists"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update simulation settings
                if 'simulation' in config_data:
                    sim_data = config_data['simulation']
                    for key, value in sim_data.items():
                        if hasattr(self.simulation, key):
                            setattr(self.simulation, key, value)
                
                # Update performance filters
                if 'performance' in config_data:
                    perf_data = config_data['performance']
                    for key, value in perf_data.items():
                        if hasattr(self.performance, key):
                            setattr(self.performance, key, value)
                
                # Update batch settings
                if 'batch' in config_data:
                    batch_data = config_data['batch']
                    for key, value in batch_data.items():
                        if hasattr(self.batch, key):
                            setattr(self.batch, key, value)
                            
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Warning: Could not load config file: {e}")
    
    def save_config(self) -> None:
        """Save current configuration to file"""
        config_data = {
            'simulation': self.simulation.__dict__,
            'performance': self.performance.__dict__,
            'batch': self.batch.__dict__
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def get_simulation_settings(self) -> Dict[str, Any]:
        """Get simulation settings as dictionary"""
        return self.simulation.to_dict()


# Global configuration instance
config = Config()
