#!/usr/bin/env python3
"""
Comprehensive test suite for BRAIN Alpha Automation System
Consolidates all testing functionality into a systematic framework
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from brain_alpha import BrainAlpha<PERSON>utomator, AlphaGenerator, PerformanceAnalyzer, Config


class SystemTester:
    """Comprehensive system testing framework"""
    
    def __init__(self):
        self.automator = BrainAlphaAutomator()
        self.generator = AlphaGenerator()
        self.analyzer = PerformanceAnalyzer()
        self.config = Config()
    
    def test_authentication(self) -> bool:
        """Test authentication system"""
        print("🔐 Testing Authentication System")
        print("-" * 40)
        
        try:
            # Check session status
            status = self.automator.session_manager.get_session_status()
            print(f"Current authentication status: {status['authenticated']}")
            print(f"Session valid: {status['session_valid']}")
            print(f"Daily logins: {status['daily_logins']}/25")
            
            # Test authentication
            if not self.automator.authenticated:
                print("Attempting authentication...")
                result = self.automator.authenticate()
                if result:
                    print("✅ Authentication successful!")
                    return True
                else:
                    print("❌ Authentication failed!")
                    return False
            else:
                print("✅ Already authenticated!")
                return True
                
        except Exception as e:
            print(f"❌ Authentication test failed: {e}")
            return False
    
    def test_expression_generation(self) -> bool:
        """Test alpha expression generation"""
        print("\n📊 Testing Expression Generation")
        print("-" * 40)
        
        try:
            # Test basic expression types
            expression_types = self.generator.get_expression_types()
            print(f"Available expression types: {len(expression_types)}")
            
            for expr_type in expression_types[:5]:  # Test first 5
                expr = self.generator.create_expression(expr_type)
                print(f"  {expr_type:15}: {expr}")
            
            # Test strategy generation
            strategies = self.generator.get_available_strategies()
            print(f"\nAvailable strategies: {strategies}")
            
            # Test momentum variations
            momentum_exprs = self.generator.generate_momentum_variations(count=3)
            print(f"\nMomentum variations (3):")
            for i, expr in enumerate(momentum_exprs, 1):
                print(f"  {i}. {expr}")
            
            # Test parameter sweeping
            template = "correlation(close, volume, {window})"
            param_ranges = {"window": [10, 15, 20]}
            sweep_exprs = self.generator.generate_parameter_sweep(template, param_ranges)
            print(f"\nParameter sweep (3):")
            for i, expr in enumerate(sweep_exprs, 1):
                print(f"  {i}. {expr}")
            
            print("✅ Expression generation tests passed!")
            return True
            
        except Exception as e:
            print(f"❌ Expression generation test failed: {e}")
            return False
    
    def test_reliable_expressions(self) -> bool:
        """Test with reliable expressions that are guaranteed to work"""
        print("\n🛡️ Testing Reliable Expressions")
        print("-" * 40)
        
        try:
            # Get reliable expressions
            reliable_expressions = self.generator.get_reliable_expressions(count=3)
            print("Testing reliable expressions:")
            for i, expr in enumerate(reliable_expressions, 1):
                print(f"  {i}. {expr}")
            
            # Test submission (but don't wait for completion)
            if self.automator.authenticated:
                print("\nTesting submission (first expression only):")
                test_expr = reliable_expressions[0]
                
                try:
                    simulation_url = self.automator.submit_alpha(test_expr)
                    print(f"✅ Successfully submitted: {test_expr}")
                    print(f"   Simulation URL: {simulation_url}")
                    print("   (Not waiting for completion in test mode)")
                    return True
                except Exception as e:
                    print(f"❌ Submission failed: {e}")
                    return False
            else:
                print("⚠️ Not authenticated - skipping submission test")
                return True
                
        except Exception as e:
            print(f"❌ Reliable expressions test failed: {e}")
            return False
    
    def test_configuration(self) -> bool:
        """Test configuration system"""
        print("\n⚙️ Testing Configuration System")
        print("-" * 40)
        
        try:
            # Test simulation settings
            sim_settings = self.config.get_simulation_settings()
            print(f"Simulation settings loaded: {len(sim_settings)} parameters")
            print(f"  Region: {sim_settings['region']}")
            print(f"  Universe: {sim_settings['universe']}")
            print(f"  Test Period: {sim_settings['testPeriod']}")
            
            # Test performance filters
            print(f"\nPerformance filters:")
            print(f"  Min Sharpe Ratio: {self.config.performance.min_sharpe_ratio}")
            print(f"  Min Fitness: {self.config.performance.min_fitness}")
            print(f"  Top N Results: {self.config.performance.top_n_results}")
            
            # Test batch settings
            print(f"\nBatch settings:")
            print(f"  Default Batch Size: {self.config.batch.default_batch_size}")
            print(f"  Max Batch Size: {self.config.batch.max_batch_size}")
            print(f"  Inter-batch Delay: {self.config.batch.inter_batch_delay}s")
            
            print("✅ Configuration tests passed!")
            return True
            
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            return False
    
    def test_performance_analysis(self) -> bool:
        """Test performance analysis functionality"""
        print("\n📈 Testing Performance Analysis")
        print("-" * 40)
        
        try:
            # Create mock results for testing
            mock_results = [
                {
                    'expression': 'rank(close)',
                    'alpha_details': {'id': 'test_alpha_1', 'fitness': 0.75},
                    'performance_data': {
                        'sharpe': {'records': [{'value': 0.85}]},
                        'pnl': {'records': [{'value': 100}, {'value': 150}]}
                    }
                },
                {
                    'expression': 'rank(volume)',
                    'alpha_details': {'id': 'test_alpha_2', 'fitness': 0.65},
                    'performance_data': {
                        'sharpe': {'records': [{'value': 0.45}]},
                        'pnl': {'records': [{'value': 80}, {'value': 120}]}
                    }
                }
            ]
            
            # Test analysis
            performance_summary = self.analyzer.analyze_results(mock_results)
            print(f"Analyzed {len(performance_summary)} mock results")
            
            for i, alpha in enumerate(performance_summary, 1):
                print(f"  {i}. {alpha['expression']}")
                print(f"     Sharpe: {alpha['sharpe_ratio']}")
                print(f"     Fitness: {alpha['fitness']}")
            
            # Test filtering
            top_performers = self.analyzer.filter_top_performers(mock_results, min_sharpe=0.5, top_n=5)
            print(f"\nTop performers (Sharpe > 0.5): {len(top_performers)}")
            
            # Test summary report
            summary_report = self.analyzer.generate_summary_report(mock_results)
            print(f"\nSummary report generated:")
            print(f"  Total alphas: {summary_report['total_alphas']}")
            print(f"  Successful alphas: {summary_report['successful_alphas']}")
            print(f"  Average Sharpe: {summary_report['avg_sharpe_ratio']:.4f}")
            
            print("✅ Performance analysis tests passed!")
            return True
            
        except Exception as e:
            print(f"❌ Performance analysis test failed: {e}")
            return False
    
    def run_quick_integration_test(self) -> bool:
        """Run a quick end-to-end integration test"""
        print("\n🚀 Quick Integration Test")
        print("-" * 40)
        
        try:
            if not self.automator.authenticated:
                print("⚠️ Not authenticated - skipping integration test")
                return True
            
            # Test with one simple expression
            test_expression = "rank(close)"
            print(f"Testing complete pipeline with: {test_expression}")
            
            # Submit
            simulation_url = self.automator.submit_alpha(test_expression)
            print(f"✅ Submitted successfully: {simulation_url}")
            
            print("⚠️ Integration test submitted but not waiting for completion")
            print("   (Use full pipeline test for complete validation)")
            
            return True
            
        except Exception as e:
            print(f"❌ Integration test failed: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all system tests"""
        print("🧪 BRAIN Alpha Automation - System Test Suite")
        print("=" * 60)
        
        tests = [
            ("Authentication", self.test_authentication),
            ("Expression Generation", self.test_expression_generation),
            ("Configuration", self.test_configuration),
            ("Performance Analysis", self.test_performance_analysis),
            ("Reliable Expressions", self.test_reliable_expressions),
            ("Quick Integration", self.run_quick_integration_test)
        ]
        
        results = {}
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name} test crashed: {e}")
                results[test_name] = False
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = 0
        total = len(tests)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:20}: {status}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! System is ready for production use.")
            return True
        else:
            print("⚠️ Some tests failed. Please review and fix issues.")
            return False


def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="BRAIN Alpha System Test Suite")
    parser.add_argument('--test', choices=['auth', 'expressions', 'config', 'performance', 'reliable', 'integration', 'all'],
                       default='all', help='Specific test to run')
    
    args = parser.parse_args()
    
    tester = SystemTester()
    
    if args.test == 'auth':
        tester.test_authentication()
    elif args.test == 'expressions':
        tester.test_expression_generation()
    elif args.test == 'config':
        tester.test_configuration()
    elif args.test == 'performance':
        tester.test_performance_analysis()
    elif args.test == 'reliable':
        tester.test_reliable_expressions()
    elif args.test == 'integration':
        tester.run_quick_integration_test()
    else:
        tester.run_all_tests()


if __name__ == "__main__":
    main()
