#!/usr/bin/env python3
"""
BRAIN Alpha Automation System - Main Entry Point

A systematic and modular approach to automated alpha strategy generation,
testing, and analysis using the WorldQuant BRAIN API.
"""

import sys
import argparse
from typing import Optional

from brain_alpha import BrainAlphaAutomator, AlphaGenerator, PerformanceAnalyzer, <PERSON>fig


def run_interactive_mode():
    """Interactive mode with menu options"""
    automator = BrainAlphaAutomator()
    generator = AlphaGenerator()
    
    print("🚀 BRAIN Alpha Automation System")
    print("=" * 50)
    print("Choose an automation strategy:")
    print("1. Basic Pipeline (3 default expressions)")
    print("2. Momentum Strategy Sweep")
    print("3. Mean Reversion Strategy Sweep")
    print("4. Correlation Strategy Sweep")
    print("5. Comprehensive Multi-Strategy")
    print("6. Custom Expression List")
    print("7. Reliable Expressions Test")
    print("8. System Status Check")
    
    choice = input("Enter choice (1-8) or press Enter for default: ").strip()
    
    if choice == "2":
        print("\n=== Momentum Strategy Sweep ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("momentum_sweep", batch_size=8)
    
    elif choice == "3":
        print("\n=== Mean Reversion Strategy Sweep ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("mean_reversion_sweep", batch_size=8)
    
    elif choice == "4":
        print("\n=== Correlation Strategy Sweep ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("correlation_sweep", batch_size=8)
    
    elif choice == "5":
        print("\n=== Comprehensive Multi-Strategy ===")
        results, summary, top = automator.batch_alpha_generation_pipeline("comprehensive", batch_size=12)
    
    elif choice == "6":
        print("\n=== Custom Expression List ===")
        custom_expressions = [
            "rank(close / ts_mean(close, 20))",
            "correlation(close, volume, 15)",
            "ts_rank(close, 10) - 0.5",
            "-(close - ts_mean(close, 15)) / ts_std(close, 15)",
            "rank(volume) * rank(close)",
            "correlation(close, volume, 20)"
        ]
        results = automator.automated_alpha_pipeline(custom_expressions=custom_expressions)
        if results:
            analyzer = PerformanceAnalyzer()
            analyzer.print_performance_summary(results)
            analyzer.save_to_csv(results)
    
    elif choice == "7":
        print("\n=== Reliable Expressions Test ===")
        reliable_expressions = generator.get_reliable_expressions(count=5)
        print("Testing reliable expressions:")
        for expr in reliable_expressions:
            print(f"  • {expr}")
        
        results = automator.automated_alpha_pipeline(custom_expressions=reliable_expressions)
        if results:
            analyzer = PerformanceAnalyzer()
            analyzer.print_performance_summary(results)
            analyzer.save_to_csv(results)
    
    elif choice == "8":
        print("\n=== System Status Check ===")
        status = automator.session_manager.get_session_status()
        print(f"Authentication Status: {'✅ Authenticated' if status['authenticated'] else '❌ Not Authenticated'}")
        print(f"Session Valid: {'✅ Valid' if status['session_valid'] else '❌ Invalid/Expired'}")
        if status['session_age_hours']:
            print(f"Session Age: {status['session_age_hours']:.1f} hours")
        print(f"Daily Logins: {status['daily_logins']}/25")
        
        if not status['authenticated']:
            print("\n🔐 Authentication needed. Run with --authenticate flag.")
        return
    
    else:
        # Default: Basic pipeline
        print("\n=== Basic Automated Alpha Pipeline ===")
        results = automator.automated_alpha_pipeline()
        
        if results:
            print(f"\n✅ Successfully processed {len(results)} alphas!")
            analyzer = PerformanceAnalyzer()
            analyzer.print_performance_summary(results, top_n=3)
            analyzer.save_to_csv(results)
    
    print("\n🎉 Automation Complete!")


def run_batch_strategy(strategy: str, batch_size: int):
    """Run a specific batch strategy"""
    automator = BrainAlphaAutomator()
    generator = AlphaGenerator()
    
    # Validate strategy
    available_strategies = generator.get_available_strategies()
    if strategy not in available_strategies:
        print(f"❌ Unknown strategy: {strategy}")
        print(f"Available strategies: {', '.join(available_strategies)}")
        return
    
    print(f"🚀 BRAIN Alpha Automation - {strategy.upper().replace('_', ' ')} Strategy")
    print(f"Batch size: {batch_size}")
    
    results, summary, top_performers = automator.batch_alpha_generation_pipeline(strategy, batch_size)
    
    if results:
        print(f"\n📊 Results Summary:")
        print(f"Total alphas processed: {len(results)}")
        print(f"Top performers: {len(top_performers) if top_performers else 0}")
        
        if top_performers:
            print(f"\n🏆 Best performing alphas:")
            for i, alpha in enumerate(top_performers[:3], 1):
                print(f"{i}. {alpha['expression']}")
                print(f"   Sharpe: {alpha['sharpe_ratio']:.4f}")
                print(f"   Alpha ID: {alpha['alpha_id']}")
    else:
        print("❌ No results generated. Check your credentials and API access.")


def run_custom_expressions(expressions: list):
    """Run custom expressions"""
    automator = BrainAlphaAutomator()
    analyzer = PerformanceAnalyzer()
    
    print(f"🚀 Running {len(expressions)} custom expressions...")
    
    results = automator.automated_alpha_pipeline(custom_expressions=expressions)
    
    if results:
        print(f"\n✅ Processed {len(results)} alphas successfully!")
        analyzer.print_performance_summary(results)
        analyzer.save_to_csv(results)
    else:
        print("❌ No results generated.")


def main():
    """Main entry point with argument parsing"""
    parser = argparse.ArgumentParser(
        description="BRAIN Alpha Automation System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Interactive mode
  python main.py --strategy momentum --batch-size 10   # Momentum strategy
  python main.py --strategy comprehensive --batch-size 15  # Comprehensive
  python main.py --authenticate                     # Just authenticate
  python main.py --status                          # Check system status
  python main.py --reliable                        # Test reliable expressions
        """
    )
    
    parser.add_argument('--strategy', choices=['momentum_sweep', 'mean_reversion_sweep', 
                                             'correlation_sweep', 'comprehensive'],
                       help='Strategy to run')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='Number of alphas to process (default: 8)')
    parser.add_argument('--authenticate', action='store_true',
                       help='Just authenticate and save session')
    parser.add_argument('--status', action='store_true',
                       help='Check system status')
    parser.add_argument('--reliable', action='store_true',
                       help='Test reliable expressions')
    parser.add_argument('--custom', nargs='+',
                       help='Run custom expressions')
    
    args = parser.parse_args()
    
    # Handle specific actions
    if args.authenticate:
        print("🔐 Authenticating with BRAIN API...")
        automator = BrainAlphaAutomator()
        if automator.authenticate():
            print("✅ Authentication successful and session saved!")
        else:
            print("❌ Authentication failed!")
        return
    
    if args.status:
        automator = BrainAlphaAutomator()
        status = automator.session_manager.get_session_status()
        print("📊 System Status:")
        print(f"  Authentication: {'✅ Active' if status['authenticated'] else '❌ Required'}")
        print(f"  Session: {'✅ Valid' if status['session_valid'] else '❌ Invalid/Expired'}")
        if status['session_age_hours']:
            print(f"  Session Age: {status['session_age_hours']:.1f} hours")
        print(f"  Daily Logins: {status['daily_logins']}/25")
        return
    
    if args.reliable:
        generator = AlphaGenerator()
        expressions = generator.get_reliable_expressions(count=5)
        run_custom_expressions(expressions)
        return
    
    if args.custom:
        run_custom_expressions(args.custom)
        return
    
    if args.strategy:
        run_batch_strategy(args.strategy, args.batch_size)
        return
    
    # Default to interactive mode
    run_interactive_mode()


if __name__ == "__main__":
    main()
